import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import request from 'supertest';
import { Express } from 'express';
import SSEService from '../app/services/sse/SSEService';
import { createTestApp } from './helpers/testApp';
import { createTestUser, generateTestToken } from './helpers/testAuth';

describe('SSE Service', () => {
  let app: Express;
  let sseService: SSEService;
  let testUser: any;
  let testToken: string;

  beforeEach(async () => {
    app = createTestApp();
    sseService = new SSEService();
    testUser = await createTestUser();
    testToken = generateTestToken(testUser);
  });

  afterEach(async () => {
    sseService.shutdown();
  });

  describe('Connection Management', () => {
    it('should create SSE connection with valid token', async () => {
      const response = await request(app)
        .get('/api/sse/connect')
        .set('Authorization', `Bearer ${testToken}`)
        .expect(200);

      // Check that response is SSE format
      expect(response.headers['content-type']).toBe('text/event-stream');
      expect(response.headers['cache-control']).toBe('no-cache');
      expect(response.headers['connection']).toBe('keep-alive');
    });

    it('should reject SSE connection without token', async () => {
      await request(app)
        .get('/api/sse/connect')
        .expect(401);
    });

    it('should reject SSE connection with invalid token', async () => {
      await request(app)
        .get('/api/sse/connect')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);
    });

    it('should track connection statistics', () => {
      const stats = sseService.getConnectionStats();
      expect(stats).toHaveProperty('totalConnections');
      expect(stats).toHaveProperty('activeConnections');
      expect(stats).toHaveProperty('connectionsByUserType');
      expect(stats).toHaveProperty('averageConnectionTime');
    });

    it('should handle multiple connections from same user', async () => {
      const clientId1 = sseService.createConnection(
        {} as any, 
        { writeHead: jest.fn(), write: jest.fn() } as any,
        testUser.id,
        'student'
      );

      const clientId2 = sseService.createConnection(
        {} as any,
        { writeHead: jest.fn(), write: jest.fn() } as any,
        testUser.id,
        'student'
      );

      expect(clientId1).not.toBe(clientId2);
      
      const connectedUsers = sseService.getConnectedUsers();
      const userConnections = connectedUsers.find(u => u.userId === testUser.id);
      expect(userConnections?.connectionCount).toBe(2);
    });
  });

  describe('Message Broadcasting', () => {
    let clientId: string;
    let mockResponse: any;

    beforeEach(() => {
      mockResponse = {
        writeHead: jest.fn(),
        write: jest.fn(),
        end: jest.fn()
      };

      clientId = sseService.createConnection(
        {} as any,
        mockResponse,
        testUser.id,
        'student'
      );
    });

    it('should send message to specific client', () => {
      const message = {
        id: 'test-message-1',
        type: 'test',
        data: { content: 'Hello World' },
        timestamp: new Date(),
        priority: 'medium' as const
      };

      const success = sseService.sendToClient(clientId, message);
      expect(success).toBe(true);
      expect(mockResponse.write).toHaveBeenCalled();
    });

    it('should send message to user (all connections)', () => {
      const message = {
        id: 'test-message-2',
        type: 'notification',
        data: { title: 'Test Notification' },
        timestamp: new Date(),
        priority: 'high' as const
      };

      const sentCount = sseService.sendToUser(testUser.id, message);
      expect(sentCount).toBe(1);
      expect(mockResponse.write).toHaveBeenCalled();
    });

    it('should broadcast to all users of specific type', () => {
      const message = {
        id: 'test-message-3',
        type: 'system_alert',
        data: { message: 'System maintenance' },
        timestamp: new Date(),
        priority: 'urgent' as const
      };

      const sentCount = sseService.sendToUserType('student', message);
      expect(sentCount).toBe(1);
    });

    it('should handle message sending to disconnected client', () => {
      // Simulate disconnected client
      mockResponse.write = jest.fn().mockImplementation(() => {
        throw new Error('Connection closed');
      });

      const message = {
        id: 'test-message-4',
        type: 'test',
        data: { content: 'Should fail' },
        timestamp: new Date(),
        priority: 'low' as const
      };

      const success = sseService.sendToClient(clientId, message);
      expect(success).toBe(false);
    });
  });

  describe('Room Management', () => {
    let clientId: string;

    beforeEach(() => {
      const mockResponse = {
        writeHead: jest.fn(),
        write: jest.fn(),
        end: jest.fn()
      };

      clientId = sseService.createConnection(
        {} as any,
        mockResponse,
        testUser.id,
        'student'
      );
    });

    it('should join client to room', () => {
      const success = sseService.joinRoom(clientId, 'course:123');
      expect(success).toBe(true);
    });

    it('should leave client from room', () => {
      sseService.joinRoom(clientId, 'course:123');
      const success = sseService.leaveRoom(clientId, 'course:123');
      expect(success).toBe(true);
    });

    it('should send message to room', () => {
      sseService.joinRoom(clientId, 'course:123');

      const message = {
        id: 'room-message-1',
        type: 'course_update',
        data: { courseId: '123', action: 'updated' },
        timestamp: new Date(),
        priority: 'medium' as const
      };

      const sentCount = sseService.sendToRoom('course:123', message);
      expect(sentCount).toBe(1);
    });

    it('should not send message to room if client not joined', () => {
      const message = {
        id: 'room-message-2',
        type: 'course_update',
        data: { courseId: '456', action: 'updated' },
        timestamp: new Date(),
        priority: 'medium' as const
      };

      const sentCount = sseService.sendToRoom('course:456', message);
      expect(sentCount).toBe(0);
    });
  });

  describe('Heartbeat and Cleanup', () => {
    it('should perform heartbeat', (done) => {
      const mockResponse = {
        writeHead: jest.fn(),
        write: jest.fn(),
        end: jest.fn()
      };

      const clientId = sseService.createConnection(
        {} as any,
        mockResponse,
        testUser.id,
        'student'
      );

      // Wait for heartbeat
      setTimeout(() => {
        expect(mockResponse.write).toHaveBeenCalledWith(
          expect.stringContaining('event: heartbeat')
        );
        done();
      }, 100);
    });

    it('should cleanup inactive connections', (done) => {
      const mockResponse = {
        writeHead: jest.fn(),
        write: jest.fn(),
        end: jest.fn()
      };

      const clientId = sseService.createConnection(
        {} as any,
        mockResponse,
        testUser.id,
        'student'
      );

      // Simulate old connection
      const client = (sseService as any).clients.get(clientId);
      if (client) {
        client.lastHeartbeat = new Date(Date.now() - 120000); // 2 minutes ago
      }

      // Wait for cleanup
      setTimeout(() => {
        const stats = sseService.getConnectionStats();
        expect(stats.activeConnections).toBe(0);
        done();
      }, 100);
    });
  });

  describe('Error Handling', () => {
    it('should handle malformed message data', () => {
      const mockResponse = {
        writeHead: jest.fn(),
        write: jest.fn(),
        end: jest.fn()
      };

      const clientId = sseService.createConnection(
        {} as any,
        mockResponse,
        testUser.id,
        'student'
      );

      const malformedMessage = {
        id: 'malformed-1',
        type: 'test',
        data: { circular: {} },
        timestamp: new Date(),
        priority: 'low' as const
      };

      // Create circular reference
      (malformedMessage.data.circular as any).self = malformedMessage.data.circular;

      // Should not throw error
      expect(() => {
        sseService.sendToClient(clientId, malformedMessage);
      }).not.toThrow();
    });

    it('should handle connection errors gracefully', () => {
      const mockResponse = {
        writeHead: jest.fn(),
        write: jest.fn().mockImplementation(() => {
          throw new Error('Network error');
        }),
        end: jest.fn()
      };

      const clientId = sseService.createConnection(
        {} as any,
        mockResponse,
        testUser.id,
        'student'
      );

      const message = {
        id: 'error-test-1',
        type: 'test',
        data: { content: 'Test' },
        timestamp: new Date(),
        priority: 'low' as const
      };

      // Should handle error gracefully
      const success = sseService.sendToClient(clientId, message);
      expect(success).toBe(false);

      // Connection should be removed
      const stats = sseService.getConnectionStats();
      expect(stats.activeConnections).toBe(0);
    });
  });

  describe('Performance', () => {
    it('should handle multiple concurrent connections', () => {
      const connections = [];
      const numConnections = 100;

      for (let i = 0; i < numConnections; i++) {
        const mockResponse = {
          writeHead: jest.fn(),
          write: jest.fn(),
          end: jest.fn()
        };

        const clientId = sseService.createConnection(
          {} as any,
          mockResponse,
          `user-${i}`,
          'student'
        );

        connections.push(clientId);
      }

      const stats = sseService.getConnectionStats();
      expect(stats.activeConnections).toBe(numConnections);

      // Test broadcasting to all connections
      const message = {
        id: 'perf-test-1',
        type: 'broadcast',
        data: { message: 'Performance test' },
        timestamp: new Date(),
        priority: 'low' as const
      };

      const sentCount = sseService.broadcast(message);
      expect(sentCount).toBe(numConnections);
    });

    it('should handle rapid message sending', () => {
      const mockResponse = {
        writeHead: jest.fn(),
        write: jest.fn(),
        end: jest.fn()
      };

      const clientId = sseService.createConnection(
        {} as any,
        mockResponse,
        testUser.id,
        'student'
      );

      const numMessages = 1000;
      let successCount = 0;

      for (let i = 0; i < numMessages; i++) {
        const message = {
          id: `rapid-${i}`,
          type: 'rapid_test',
          data: { index: i },
          timestamp: new Date(),
          priority: 'low' as const
        };

        if (sseService.sendToClient(clientId, message)) {
          successCount++;
        }
      }

      expect(successCount).toBe(numMessages);
      expect(mockResponse.write).toHaveBeenCalledTimes(numMessages + 1); // +1 for welcome message
    });
  });
});

// Helper functions for test setup
function createMockRequest(): any {
  return {
    headers: {},
    ip: '127.0.0.1',
    on: jest.fn()
  };
}

function createMockResponse(): any {
  return {
    writeHead: jest.fn(),
    write: jest.fn(),
    end: jest.fn(),
    on: jest.fn()
  };
}
