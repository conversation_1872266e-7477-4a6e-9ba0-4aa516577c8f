import express, { Express } from 'express';
import cors from 'cors';
import sseRoutes from '../../app/routes/sseRoutes';
import pollingRoutes from '../../app/routes/pollingRoutes';
import monitoringRoutes from '../../app/routes/monitoringRoutes';

export function createTestApp(): Express {
  const app = express();

  // Basic middleware
  app.use(cors());
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));

  // Test routes
  app.use('/api/sse', sseRoutes);
  app.use('/api/polling', pollingRoutes);
  app.use('/api/monitoring', monitoringRoutes);

  // Error handling middleware
  app.use((error: any, req: any, res: any, next: any) => {
    res.status(error.statusCode || 500).json({
      error: error.message || 'Internal server error',
      stack: process.env.NODE_ENV === 'test' ? error.stack : undefined
    });
  });

  return app;
}
