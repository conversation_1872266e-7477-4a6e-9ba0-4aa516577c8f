import jwt from 'jsonwebtoken';

export interface TestUser {
  id: string;
  email: string;
  role: 'student' | 'teacher' | 'admin';
  name: string;
}

export function createTestUser(overrides: Partial<TestUser> = {}): TestUser {
  return {
    id: 'test-user-123',
    email: '<EMAIL>',
    role: 'student',
    name: 'Test User',
    ...overrides
  };
}

export function generateTestToken(user: TestUser): string {
  const payload = {
    userId: user.id,
    email: user.email,
    role: user.role,
    name: user.name
  };

  return jwt.sign(payload, process.env.JWT_ACCESS_SECRET || 'test-secret', {
    expiresIn: '1h'
  });
}

export function createTestTeacher(): TestUser {
  return createTestUser({
    id: 'test-teacher-456',
    email: '<EMAIL>',
    role: 'teacher',
    name: 'Test Teacher'
  });
}

export function createTestAdmin(): TestUser {
  return createTestUser({
    id: 'test-admin-789',
    email: '<EMAIL>',
    role: 'admin',
    name: 'Test Admin'
  });
}
