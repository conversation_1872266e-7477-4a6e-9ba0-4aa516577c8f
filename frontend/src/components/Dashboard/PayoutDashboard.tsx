import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  DollarSign, 
  Calendar, 
  TrendingUp, 
  Clock, 
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Settings,
  Download,
  Eye
} from 'lucide-react';
import { toast } from 'sonner';
import { useWebSocket } from '@/hooks/useWebSocket';
import { usePayouts } from '@/hooks/usePayouts';

interface PayoutDashboardProps {
  teacherId?: string;
}

interface PayoutSummary {
  pendingEarnings: {
    amount: number;
    currency: string;
    transactionCount: number;
  };
  nextPayoutDate: string | null;
  lastPayoutAmount: number;
  totalEarnings: number;
  payoutHistory: PayoutRecord[];
  analytics: PayoutAnalytics;
}

interface PayoutRecord {
  id: string;
  amount: number;
  currency: string;
  status: 'scheduled' | 'processing' | 'completed' | 'failed';
  scheduledAt: string;
  completedAt?: string;
  failureReason?: string;
  estimatedArrival?: string;
}

interface PayoutAnalytics {
  totalPayouts: number;
  successRate: number;
  averageAmount: number;
  averageProcessingTime: number;
  monthlyTrend: Array<{
    month: string;
    amount: number;
    count: number;
  }>;
}

const PayoutDashboard: React.FC<PayoutDashboardProps> = ({ teacherId }) => {
  const [payoutSummary, setPayoutSummary] = useState<PayoutSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const { isConnected, lastMessage } = useWebSocket();
  const { 
    getPayoutSummary, 
    requestPayout, 
    getPayoutHistory,
    updatePayoutPreferences 
  } = usePayouts();

  // Load initial data
  useEffect(() => {
    loadPayoutData();
  }, [teacherId]);

  // Listen for real-time payout updates
  useEffect(() => {
    if (lastMessage?.type === 'payout-updated' && lastMessage?.data?.teacherId === teacherId) {
      const { payoutId, status, amount } = lastMessage.data;
      
      // Update payout in history
      setPayoutSummary(prev => {
        if (!prev) return prev;
        
        const updatedHistory = prev.payoutHistory.map(payout => 
          payout.id === payoutId 
            ? { ...payout, status, completedAt: status === 'completed' ? new Date().toISOString() : payout.completedAt }
            : payout
        );

        return {
          ...prev,
          payoutHistory: updatedHistory,
        };
      });

      // Show notification
      if (status === 'completed') {
        toast.success(`Payout Completed: $${amount}`, {
          description: 'Your payout has been successfully processed.',
        });
      } else if (status === 'failed') {
        toast.error(`Payout Failed: $${amount}`, {
          description: 'Please check your bank account details.',
        });
      }
    }
  }, [lastMessage, teacherId]);

  const loadPayoutData = async () => {
    try {
      setLoading(true);
      const summary = await getPayoutSummary(teacherId);
      setPayoutSummary(summary);
    } catch (error: any) {
      toast.error('Failed to load payout data', {
        description: error.message,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      await loadPayoutData();
      toast.success('Payout data refreshed');
    } catch (error: any) {
      toast.error('Failed to refresh data', {
        description: error.message,
      });
    } finally {
      setRefreshing(false);
    }
  };

  const handleRequestPayout = async () => {
    try {
      if (!payoutSummary?.pendingEarnings.amount) {
        toast.error('No pending earnings available for payout');
        return;
      }

      await requestPayout(teacherId, {
        amount: payoutSummary.pendingEarnings.amount,
      });
      
      toast.success('Payout requested successfully', {
        description: 'Your payout will be processed within 1-2 business days.',
      });
      
      await loadPayoutData();
    } catch (error: any) {
      toast.error('Failed to request payout', {
        description: error.message,
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'processing': return 'bg-blue-100 text-blue-800';
      case 'scheduled': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      case 'processing': return <RefreshCw className="h-4 w-4 animate-spin" />;
      case 'scheduled': return <Clock className="h-4 w-4" />;
      case 'failed': return <AlertCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!payoutSummary) {
    return (
      <Card>
        <CardContent className="py-8 text-center">
          <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">Unable to load payout data</p>
          <Button onClick={loadPayoutData} className="mt-4">
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Payout Dashboard</h2>
        <div className="flex items-center space-x-2">
          {isConnected && (
            <Badge variant="outline" className="text-green-600 border-green-600">
              <div className="h-2 w-2 bg-green-500 rounded-full mr-1 animate-pulse"></div>
              Live Updates
            </Badge>
          )}
          <Button variant="outline" size="sm" onClick={handleRefresh} disabled={refreshing}>
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending Earnings</p>
                <p className="text-2xl font-bold">
                  ${payoutSummary.pendingEarnings.amount.toFixed(2)}
                </p>
                <p className="text-xs text-gray-500">
                  {payoutSummary.pendingEarnings.transactionCount} transactions
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Next Payout</p>
                <p className="text-lg font-semibold">
                  {payoutSummary.nextPayoutDate 
                    ? new Date(payoutSummary.nextPayoutDate).toLocaleDateString()
                    : 'Manual'
                  }
                </p>
                <p className="text-xs text-gray-500">Scheduled</p>
              </div>
              <Calendar className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Earnings</p>
                <p className="text-2xl font-bold">
                  ${payoutSummary.totalEarnings.toFixed(2)}
                </p>
                <p className="text-xs text-gray-500">All time</p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Success Rate</p>
                <p className="text-2xl font-bold">
                  {payoutSummary.analytics.successRate.toFixed(1)}%
                </p>
                <p className="text-xs text-gray-500">
                  {payoutSummary.analytics.totalPayouts} payouts
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold">Ready for Payout</h3>
              <p className="text-sm text-gray-600">
                You have ${payoutSummary.pendingEarnings.amount.toFixed(2)} available for payout
              </p>
            </div>
            <Button 
              onClick={handleRequestPayout}
              disabled={payoutSummary.pendingEarnings.amount <= 0}
            >
              Request Payout
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Detailed View */}
      <Tabs defaultValue="history" className="space-y-4">
        <TabsList>
          <TabsTrigger value="history">Payout History</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Payouts</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {payoutSummary.payoutHistory.map((payout) => (
                  <div key={payout.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      {getStatusIcon(payout.status)}
                      <div>
                        <p className="font-medium">${payout.amount.toFixed(2)}</p>
                        <p className="text-sm text-gray-500">
                          {new Date(payout.scheduledAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge className={getStatusColor(payout.status)}>
                        {payout.status}
                      </Badge>
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Payout Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium mb-2">Average Processing Time</h4>
                  <p className="text-2xl font-bold">
                    {Math.round(payoutSummary.analytics.averageProcessingTime / 1000 / 60)} min
                  </p>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Average Payout Amount</h4>
                  <p className="text-2xl font-bold">
                    ${payoutSummary.analytics.averageAmount.toFixed(2)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Payout Preferences</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Configure your payout schedule and preferences.</p>
              <Button className="mt-4">
                <Settings className="h-4 w-4 mr-2" />
                Manage Settings
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PayoutDashboard;
