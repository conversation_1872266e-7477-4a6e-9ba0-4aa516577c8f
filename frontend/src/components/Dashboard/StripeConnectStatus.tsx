import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  ExternalLink, 
  RefreshCw,
  CreditCard,
  Bank,
  Shield,
  TrendingUp
} from 'lucide-react';
import { toast } from 'sonner';
import { useWebSocket } from '@/hooks/useWebSocket';
import { useStripeConnect } from '@/hooks/useStripeConnect';

interface StripeConnectStatusProps {
  teacherId?: string;
  showActions?: boolean;
  compact?: boolean;
}

interface AccountStatus {
  status: 'pending' | 'connected' | 'restricted' | 'disconnected';
  verified: boolean;
  onboardingComplete: boolean;
  requirements: string[];
  capabilities: {
    card_payments?: string;
    transfers?: string;
  };
  accountHealthScore: number;
  lastStatusUpdate: string;
  failureReason?: string;
}

const StripeConnectStatus: React.FC<StripeConnectStatusProps> = ({
  teacherId,
  showActions = true,
  compact = false
}) => {
  const [accountStatus, setAccountStatus] = useState<AccountStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const { isConnected, lastMessage } = useWebSocket();
  const { 
    getAccountStatus, 
    createOnboardingLink, 
    refreshAccountStatus 
  } = useStripeConnect();

  // Load initial account status
  useEffect(() => {
    loadAccountStatus();
  }, [teacherId]);

  // Listen for real-time updates
  useEffect(() => {
    if (lastMessage?.type === 'stripe-account-updated' && lastMessage?.data?.teacherId === teacherId) {
      setAccountStatus(prev => ({
        ...prev,
        ...lastMessage.data.accountStatus
      }));
      
      // Show toast notification for status changes
      if (lastMessage.data.statusChanged) {
        const { newStatus, message } = lastMessage.data;
        toast.success(`Account Status Updated: ${newStatus}`, {
          description: message,
        });
      }
    }
  }, [lastMessage, teacherId]);

  const loadAccountStatus = async () => {
    try {
      setLoading(true);
      const status = await getAccountStatus(teacherId);
      setAccountStatus(status);
    } catch (error: any) {
      toast.error('Failed to load account status', {
        description: error.message,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      const status = await refreshAccountStatus(teacherId);
      setAccountStatus(status);
      toast.success('Account status refreshed');
    } catch (error: any) {
      toast.error('Failed to refresh status', {
        description: error.message,
      });
    } finally {
      setRefreshing(false);
    }
  };

  const handleStartOnboarding = async () => {
    try {
      const { url } = await createOnboardingLink(teacherId);
      window.open(url, '_blank');
    } catch (error: any) {
      toast.error('Failed to create onboarding link', {
        description: error.message,
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'bg-green-500';
      case 'pending': return 'bg-yellow-500';
      case 'restricted': return 'bg-red-500';
      case 'disconnected': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected': return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'pending': return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'restricted': return <AlertCircle className="h-5 w-5 text-red-500" />;
      case 'disconnected': return <AlertCircle className="h-5 w-5 text-gray-500" />;
      default: return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    if (score >= 40) return 'text-orange-600';
    return 'text-red-600';
  };

  if (loading) {
    return (
      <Card className={compact ? 'p-4' : ''}>
        <CardContent className="flex items-center justify-center py-8">
          <RefreshCw className="h-6 w-6 animate-spin" />
          <span className="ml-2">Loading account status...</span>
        </CardContent>
      </Card>
    );
  }

  if (!accountStatus) {
    return (
      <Card className={compact ? 'p-4' : ''}>
        <CardContent className="py-8">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Unable to load Stripe account status. Please try again.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (compact) {
    return (
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {getStatusIcon(accountStatus.status)}
              <div>
                <p className="font-medium capitalize">{accountStatus.status}</p>
                <p className="text-sm text-gray-500">
                  Health: {accountStatus.accountHealthScore}%
                </p>
              </div>
            </div>
            {isConnected && (
              <div className="flex items-center space-x-1">
                <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-xs text-gray-500">Live</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-lg font-medium">Stripe Connect Status</CardTitle>
        <div className="flex items-center space-x-2">
          {isConnected && (
            <Badge variant="outline" className="text-green-600 border-green-600">
              <div className="h-2 w-2 bg-green-500 rounded-full mr-1 animate-pulse"></div>
              Live Updates
            </Badge>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Status Overview */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {getStatusIcon(accountStatus.status)}
            <div>
              <p className="font-medium capitalize">{accountStatus.status}</p>
              <p className="text-sm text-gray-500">
                Last updated: {new Date(accountStatus.lastStatusUpdate).toLocaleString()}
              </p>
            </div>
          </div>
          <Badge 
            variant={accountStatus.status === 'connected' ? 'default' : 'secondary'}
            className={accountStatus.status === 'connected' ? 'bg-green-100 text-green-800' : ''}
          >
            {accountStatus.status === 'connected' ? 'Active' : 'Setup Required'}
          </Badge>
        </div>

        {/* Account Health Score */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Account Health</span>
            <span className={`text-sm font-bold ${getHealthScoreColor(accountStatus.accountHealthScore)}`}>
              {accountStatus.accountHealthScore}%
            </span>
          </div>
          <Progress 
            value={accountStatus.accountHealthScore} 
            className="h-2"
          />
          <p className="text-xs text-gray-500">
            Based on verification status, capabilities, and compliance
          </p>
        </div>

        {/* Capabilities */}
        <div className="grid grid-cols-2 gap-4">
          <div className="flex items-center space-x-2">
            <CreditCard className="h-4 w-4 text-gray-400" />
            <span className="text-sm">Card Payments</span>
            <Badge 
              variant={accountStatus.capabilities.card_payments === 'active' ? 'default' : 'secondary'}
              className="ml-auto"
            >
              {accountStatus.capabilities.card_payments || 'inactive'}
            </Badge>
          </div>
          <div className="flex items-center space-x-2">
            <Bank className="h-4 w-4 text-gray-400" />
            <span className="text-sm">Transfers</span>
            <Badge 
              variant={accountStatus.capabilities.transfers === 'active' ? 'default' : 'secondary'}
              className="ml-auto"
            >
              {accountStatus.capabilities.transfers || 'inactive'}
            </Badge>
          </div>
        </div>

        {/* Requirements */}
        {accountStatus.requirements.length > 0 && (
          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription>
              <p className="font-medium mb-2">Action Required:</p>
              <ul className="list-disc list-inside space-y-1 text-sm">
                {accountStatus.requirements.map((req, index) => (
                  <li key={index}>{req.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {/* Failure Reason */}
        {accountStatus.failureReason && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {accountStatus.failureReason}
            </AlertDescription>
          </Alert>
        )}

        {/* Actions */}
        {showActions && (
          <div className="flex space-x-2">
            {accountStatus.status !== 'connected' && (
              <Button onClick={handleStartOnboarding} className="flex-1">
                <ExternalLink className="h-4 w-4 mr-2" />
                {accountStatus.onboardingComplete ? 'Complete Setup' : 'Start Onboarding'}
              </Button>
            )}
            {accountStatus.status === 'connected' && (
              <Button variant="outline" className="flex-1">
                <TrendingUp className="h-4 w-4 mr-2" />
                View Earnings
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default StripeConnectStatus;
