import { useState, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { apiClient } from '@/lib/apiClient';

interface PayoutSummary {
  pendingEarnings: {
    amount: number;
    currency: string;
    transactionCount: number;
  };
  nextPayoutDate: string | null;
  lastPayoutAmount: number;
  totalEarnings: number;
  payoutHistory: PayoutRecord[];
  analytics: PayoutAnalytics;
}

interface PayoutRecord {
  id: string;
  amount: number;
  currency: string;
  status: 'scheduled' | 'processing' | 'completed' | 'failed';
  scheduledAt: string;
  completedAt?: string;
  failureReason?: string;
  estimatedArrival?: string;
  stripePayoutId?: string;
  description?: string;
}

interface PayoutAnalytics {
  totalPayouts: number;
  successRate: number;
  averageAmount: number;
  averageProcessingTime: number;
  monthlyTrend: Array<{
    month: string;
    amount: number;
    count: number;
  }>;
}

interface PayoutPreferences {
  schedule: 'daily' | 'weekly' | 'biweekly' | 'monthly' | 'manual';
  minimumAmount: number;
  isAutoPayoutEnabled: boolean;
  notificationChannels: string[];
  customSchedule?: {
    dayOfWeek?: number;
    dayOfMonth?: number;
    hour?: number;
    timezone?: string;
  };
}

interface PayoutRequest {
  amount?: number;
  description?: string;
  scheduledAt?: string;
}

interface UsePayoutsReturn {
  summary: PayoutSummary | null;
  preferences: PayoutPreferences | null;
  loading: boolean;
  error: string | null;
  getPayoutSummary: (teacherId?: string) => Promise<PayoutSummary>;
  getPayoutHistory: (teacherId?: string, options?: any) => Promise<PayoutRecord[]>;
  getPayoutPreferences: (teacherId?: string) => Promise<PayoutPreferences>;
  updatePayoutPreferences: (teacherId?: string, preferences: Partial<PayoutPreferences>) => Promise<PayoutPreferences>;
  requestPayout: (teacherId?: string, request: PayoutRequest) => Promise<PayoutRecord>;
  cancelPayout: (payoutId: string) => Promise<void>;
  getPayoutDetails: (payoutId: string) => Promise<PayoutRecord>;
  getPayoutAnalytics: (teacherId?: string, startDate?: string, endDate?: string) => Promise<PayoutAnalytics>;
}

export const usePayouts = (): UsePayoutsReturn => {
  const [summary, setSummary] = useState<PayoutSummary | null>(null);
  const [preferences, setPreferences] = useState<PayoutPreferences | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { user, token } = useAuth();

  // Get payout summary
  const getPayoutSummary = useCallback(async (teacherId?: string): Promise<PayoutSummary> => {
    try {
      setLoading(true);
      setError(null);

      const id = teacherId || user?.id;
      if (!id) {
        throw new Error('Teacher ID is required');
      }

      const response = await apiClient.get(`/payouts/summary/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const summaryData = response.data.data;
      setSummary(summaryData);
      return summaryData;
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to get payout summary';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [user, token]);

  // Get payout history
  const getPayoutHistory = useCallback(async (teacherId?: string, options: any = {}): Promise<PayoutRecord[]> => {
    try {
      setLoading(true);
      setError(null);

      const id = teacherId || user?.id;
      if (!id) {
        throw new Error('Teacher ID is required');
      }

      const params = new URLSearchParams({
        limit: options.limit?.toString() || '50',
        offset: options.offset?.toString() || '0',
        ...options,
      });

      const response = await apiClient.get(`/payouts/history/${id}?${params}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return response.data.data.payouts;
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to get payout history';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [user, token]);

  // Get payout preferences
  const getPayoutPreferences = useCallback(async (teacherId?: string): Promise<PayoutPreferences> => {
    try {
      setLoading(true);
      setError(null);

      const id = teacherId || user?.id;
      if (!id) {
        throw new Error('Teacher ID is required');
      }

      const response = await apiClient.get(`/payouts/preferences/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const preferencesData = response.data.data;
      setPreferences(preferencesData);
      return preferencesData;
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to get payout preferences';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [user, token]);

  // Update payout preferences
  const updatePayoutPreferences = useCallback(async (
    teacherId?: string, 
    preferencesUpdate: Partial<PayoutPreferences>
  ): Promise<PayoutPreferences> => {
    try {
      setLoading(true);
      setError(null);

      const id = teacherId || user?.id;
      if (!id) {
        throw new Error('Teacher ID is required');
      }

      const response = await apiClient.put(`/payouts/preferences/${id}`, preferencesUpdate, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const preferencesData = response.data.data;
      setPreferences(preferencesData);
      return preferencesData;
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to update payout preferences';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [user, token]);

  // Request payout
  const requestPayout = useCallback(async (teacherId?: string, request: PayoutRequest): Promise<PayoutRecord> => {
    try {
      setLoading(true);
      setError(null);

      const id = teacherId || user?.id;
      if (!id) {
        throw new Error('Teacher ID is required');
      }

      const response = await apiClient.post(`/payouts/request`, {
        teacherId: id,
        ...request,
      }, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return response.data.data;
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to request payout';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [user, token]);

  // Cancel payout
  const cancelPayout = useCallback(async (payoutId: string): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      await apiClient.post(`/payouts/${payoutId}/cancel`, {}, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to cancel payout';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [token]);

  // Get payout details
  const getPayoutDetails = useCallback(async (payoutId: string): Promise<PayoutRecord> => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiClient.get(`/payouts/${payoutId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return response.data.data;
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to get payout details';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [token]);

  // Get payout analytics
  const getPayoutAnalytics = useCallback(async (
    teacherId?: string, 
    startDate?: string, 
    endDate?: string
  ): Promise<PayoutAnalytics> => {
    try {
      setLoading(true);
      setError(null);

      const id = teacherId || user?.id;
      if (!id) {
        throw new Error('Teacher ID is required');
      }

      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const response = await apiClient.get(`/payouts/analytics/${id}?${params}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return response.data.data;
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to get payout analytics';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [user, token]);

  return {
    summary,
    preferences,
    loading,
    error,
    getPayoutSummary,
    getPayoutHistory,
    getPayoutPreferences,
    updatePayoutPreferences,
    requestPayout,
    cancelPayout,
    getPayoutDetails,
    getPayoutAnalytics,
  };
};
