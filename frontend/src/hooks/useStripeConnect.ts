import { useState, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { apiClient } from '@/lib/apiClient';

interface StripeConnectAccount {
  status: 'pending' | 'connected' | 'restricted' | 'disconnected';
  verified: boolean;
  onboardingComplete: boolean;
  requirements: string[];
  capabilities: {
    card_payments?: string;
    transfers?: string;
  };
  accountHealthScore: number;
  lastStatusUpdate: string;
  failureReason?: string;
  onboardingUrl?: string;
  accountId?: string;
}

interface OnboardingLinkResponse {
  url: string;
  expiresAt: string;
}

interface UseStripeConnectReturn {
  account: StripeConnectAccount | null;
  loading: boolean;
  error: string | null;
  getAccountStatus: (teacherId?: string) => Promise<StripeConnectAccount>;
  createOnboardingLink: (teacherId?: string) => Promise<OnboardingLinkResponse>;
  refreshAccountStatus: (teacherId?: string) => Promise<StripeConnectAccount>;
  disconnectAccount: (teacherId?: string) => Promise<void>;
  updateAccountInfo: (teacherId?: string, data: any) => Promise<StripeConnectAccount>;
}

export const useStripeConnect = (): UseStripeConnectReturn => {
  const [account, setAccount] = useState<StripeConnectAccount | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { user, token } = useAuth();

  // Get account status
  const getAccountStatus = useCallback(async (teacherId?: string): Promise<StripeConnectAccount> => {
    try {
      setLoading(true);
      setError(null);

      const id = teacherId || user?.id;
      if (!id) {
        throw new Error('Teacher ID is required');
      }

      const response = await apiClient.get(`/stripe-connect/status/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const accountData = response.data.data;
      setAccount(accountData);
      return accountData;
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to get account status';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [user, token]);

  // Create onboarding link
  const createOnboardingLink = useCallback(async (teacherId?: string): Promise<OnboardingLinkResponse> => {
    try {
      setLoading(true);
      setError(null);

      const id = teacherId || user?.id;
      if (!id) {
        throw new Error('Teacher ID is required');
      }

      const response = await apiClient.post(`/stripe-connect/onboarding-link`, {
        teacherId: id,
        refreshUrl: `${window.location.origin}/teacher/stripe-connect?refresh=true`,
        returnUrl: `${window.location.origin}/teacher/stripe-connect?success=true`,
      }, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return response.data.data;
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to create onboarding link';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [user, token]);

  // Refresh account status from Stripe
  const refreshAccountStatus = useCallback(async (teacherId?: string): Promise<StripeConnectAccount> => {
    try {
      setLoading(true);
      setError(null);

      const id = teacherId || user?.id;
      if (!id) {
        throw new Error('Teacher ID is required');
      }

      const response = await apiClient.post(`/stripe-connect/refresh-status`, {
        teacherId: id,
      }, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const accountData = response.data.data;
      setAccount(accountData);
      return accountData;
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to refresh account status';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [user, token]);

  // Disconnect account
  const disconnectAccount = useCallback(async (teacherId?: string): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      const id = teacherId || user?.id;
      if (!id) {
        throw new Error('Teacher ID is required');
      }

      await apiClient.post(`/stripe-connect/disconnect`, {
        teacherId: id,
      }, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      // Update local state
      setAccount(prev => prev ? {
        ...prev,
        status: 'disconnected',
        accountId: undefined,
        onboardingUrl: undefined,
      } : null);
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to disconnect account';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [user, token]);

  // Update account information
  const updateAccountInfo = useCallback(async (teacherId?: string, data: any): Promise<StripeConnectAccount> => {
    try {
      setLoading(true);
      setError(null);

      const id = teacherId || user?.id;
      if (!id) {
        throw new Error('Teacher ID is required');
      }

      const response = await apiClient.put(`/stripe-connect/account/${id}`, data, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const accountData = response.data.data;
      setAccount(accountData);
      return accountData;
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to update account';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [user, token]);

  return {
    account,
    loading,
    error,
    getAccountStatus,
    createOnboardingLink,
    refreshAccountStatus,
    disconnectAccount,
    updateAccountInfo,
  };
};
