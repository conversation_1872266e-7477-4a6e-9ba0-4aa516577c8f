# Green Uni Mind - Developer Guide

## Project Overview

**Green Uni Mind** is a comprehensive online learning platform built with modern web technologies. It's an enterprise-level e-learning application that supports course creation, student enrollment, payment processing, real-time messaging, and advanced analytics.

### Core Technologies
- **Backend**: Node.js, TypeScript, Express.js, MongoDB, Redis
- **Frontend**: React 18, TypeScript, Vite, Tailwind CSS, shadcn/ui
- **Authentication**: JWT, OAuth (Google, Facebook, Apple), 2FA
- **Payment**: Stripe Connect with multi-vendor support
- **Real-time**: WebSocket, Server-Sent Events (SSE)
- **Deployment**: Docker, Render, Cloudflare Pages
- **Testing**: Jest, Cypress
- **State Management**: Redux Toolkit with RTK Query

## Project Structure

### Backend (`/backend`)
```
backend/
├── src/
│   ├── app/
│   │   ├── modules/          # Feature modules (Auth, Course, Payment, etc.)
│   │   ├── middlewares/      # Express middlewares
│   │   ├── services/         # Business logic services
│   │   ├── config/           # Configuration files
│   │   ├── utils/            # Utility functions
│   │   └── routes/           # API route definitions
│   ├── scripts/              # Database seeding and utilities
│   └── tests/                # Test files
├── Dockerfile                # Production Docker configuration
├── docker-compose.yml        # Local development setup
└── package.json              # Dependencies and scripts
```

### Frontend (`/client`)
```
client/
├── src/
│   ├── components/           # Reusable UI components
│   │   ├── ui/              # shadcn/ui components
│   │   ├── Course/          # Course-related components
│   │   ├── Teacher/         # Teacher dashboard components
│   │   └── Analytics/       # Analytics components
│   ├── pages/               # Route components
│   ├── redux/               # State management
│   ├── hooks/               # Custom React hooks
│   ├── services/            # API services
│   └── utils/               # Utility functions
├── public/                  # Static assets
├── vite.config.ts           # Vite configuration
└── tailwind.config.ts       # Tailwind CSS configuration
```

## Development Guidelines

### Code Organization
- **Modular Architecture**: Each feature is organized as a module with its own controller, service, model, and routes
- **Component-Based**: Frontend follows atomic design principles with reusable components
- **Type Safety**: Strict TypeScript configuration with comprehensive type definitions
- **API-First**: RESTful API design with consistent response patterns

### Naming Conventions
- **Files**: camelCase for TypeScript files, kebab-case for component files
- **Components**: PascalCase (e.g., `CourseCard.tsx`)
- **Services**: camelCase with `.service.ts` suffix
- **API Routes**: kebab-case with version prefix (`/api/v1/`)
- **Database Models**: PascalCase with `.model.ts` suffix

### Environment Configuration
- **Backend**: Uses `.env` files with comprehensive environment variables
- **Frontend**: Uses `VITE_` prefixed environment variables
- **Production**: Environment variables managed through Render dashboard
- **Security**: Sensitive keys stored as secrets, never committed to repository

## Key Features

### Authentication & Authorization
- JWT-based authentication with refresh tokens
- OAuth integration (Google, Facebook, Apple)
- Two-factor authentication (2FA) with QR codes
- Role-based access control (Student, Teacher, Admin)
- Session management with Redis caching

### Course Management
- Rich course creation with video uploads
- Lecture organization with progress tracking
- AI-powered content enhancement
- Real-time collaboration features
- Advanced analytics and reporting

### Payment System
- Stripe Connect for multi-vendor payments
- Automated payout management
- Transaction tracking and reporting
- Invoice generation
- Revenue analytics

### Performance Optimizations
- Redis caching for frequently accessed data
- Optimistic updates for better UX
- Image optimization with Cloudinary
- Code splitting and lazy loading
- Service worker for offline support

## Development Workflow

### Local Development Setup
```bash
# Backend setup
cd backend
npm install
npm run dev

# Frontend setup
cd client
npm install
npm run dev

# Docker development
cd backend
docker-compose up -d
```

### Testing
```bash
# Backend tests
cd backend
npm test
npm run test:coverage

# Frontend tests
cd client
npm test

# E2E tests
cd client
npm run cypress:open
```

### Build & Deployment
```bash
# Backend build
cd backend
npm run build
npm run prod

# Frontend build
cd client
npm run build
npm run preview
```

## API Design Patterns

### Request/Response Structure
- **Success Response**: `{ success: true, data: {...}, message: "..." }`
- **Error Response**: `{ success: false, message: "...", errorSources: [...] }`
- **Pagination**: `{ data: [...], meta: { page, limit, total, totalPages } }`

### Authentication Headers
- **Authorization**: `Bearer <jwt_token>`
- **Refresh Token**: Sent via secure HTTP-only cookies

### Error Handling
- Centralized error handling middleware
- Consistent error response format
- Proper HTTP status codes
- Detailed error logging

## Security Best Practices

### Data Protection
- AES-256-GCM encryption for sensitive data
- Secure cookie configuration (HttpOnly, Secure, SameSite)
- Request/response encryption for critical endpoints
- Input validation with Zod schemas

### Security Headers
- Content Security Policy (CSP)
- HTTP Strict Transport Security (HSTS)
- X-Frame-Options, X-Content-Type-Options
- Rate limiting and DDoS protection

### Authentication Security
- Secure password hashing with bcrypt
- JWT token rotation and blacklisting
- OAuth state parameter validation
- 2FA implementation with time-based OTP

## Performance Guidelines

### Backend Optimization
- Redis caching strategy for database queries
- Connection pooling for database and Redis
- Lazy loading of heavy modules
- Background job processing with BullMQ

### Frontend Optimization
- Code splitting with dynamic imports
- Image optimization and lazy loading
- Virtual scrolling for large lists
- Memoization of expensive computations

### Database Optimization
- Proper indexing strategy
- Query optimization with aggregation pipelines
- Connection pooling and timeout configuration
- Regular performance monitoring

## Deployment Configuration

### Docker Setup
- Multi-stage builds for optimized images
- Non-root user for security
- Health checks for container monitoring
- Volume mounting for persistent data

### Environment Variables
- Comprehensive environment configuration
- Secure secret management
- Feature flags for gradual rollouts
- Environment-specific configurations

### Monitoring & Logging
- Structured logging with Winston
- Performance monitoring with custom metrics
- Error tracking and alerting
- Health check endpoints

## Common Patterns

### Service Layer Pattern
```typescript
// Service implementation
export class CourseService {
  static async createCourse(payload: CreateCoursePayload) {
    // Business logic here
  }
}

// Controller usage
const createCourse = catchAsync(async (req, res) => {
  const result = await CourseService.createCourse(req.body);
  sendResponse(res, {
    statusCode: httpStatus.CREATED,
    success: true,
    message: 'Course created successfully',
    data: result,
  });
});
```

### React Component Pattern
```typescript
// Component with hooks and error handling
const CourseCard: React.FC<CourseCardProps> = ({ course }) => {
  const [isLoading, setIsLoading] = useState(false);
  const { data, error, isLoading: queryLoading } = useGetCourseQuery(course.id);
  
  if (error) return <ErrorBoundary error={error} />;
  if (queryLoading) return <CourseCardSkeleton />;
  
  return (
    <Card className="course-card">
      {/* Component content */}
    </Card>
  );
};
```

### API Integration Pattern
```typescript
// RTK Query API slice
export const courseApi = createApi({
  reducerPath: 'courseApi',
  baseQuery: baseQueryWithAuth,
  tagTypes: ['Course'],
  endpoints: (builder) => ({
    getCourses: builder.query<CoursesResponse, GetCoursesParams>({
      query: (params) => ({
        url: '/courses',
        params,
      }),
      providesTags: ['Course'],
    }),
  }),
});
```

## Troubleshooting

### Common Issues
- **Redis Connection**: Check Redis URL and authentication
- **Database Connection**: Verify MongoDB connection string
- **Build Failures**: Check TypeScript compilation errors
- **CORS Issues**: Verify frontend/backend URL configuration

### Debug Commands
```bash
# Backend debugging
npm run dev -- --inspect
npm run test:watch

# Frontend debugging
npm run dev -- --debug
npm run build -- --mode development

# Docker debugging
docker logs <container_name>
docker exec -it <container_name> /bin/sh
```

### Performance Monitoring
```bash
# Backend performance
npm run performance:monitor
npm run enhanced:health

# Database optimization
npm run optimize:db
npm run optimize:cache
```

## Contributing Guidelines

### Code Quality
- Follow ESLint and Prettier configurations
- Write comprehensive tests for new features
- Use TypeScript strict mode
- Document complex business logic

### Git Workflow
- Feature branches from `main`
- Descriptive commit messages
- Pull request reviews required
- Automated testing before merge

### Documentation
- Update API documentation for new endpoints
- Add JSDoc comments for complex functions
- Update README files for new features
- Maintain changelog for releases