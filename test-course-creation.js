const FormData = require('form-data');
const fetch = require('node-fetch');

async function testCourseCreation() {
  const formData = new FormData();
  
  // Add basic course fields
  formData.append('title', 'Test Course Title');
  formData.append('subtitle', 'Test Course Subtitle');
  formData.append('description', 'This is a test course description that is long enough to meet the minimum requirements for course creation.');
  formData.append('categoryId', '507f1f77bcf86cd799439011');
  formData.append('subcategoryId', '507f1f77bcf86cd799439012');
  formData.append('courseLevel', 'Beginner');
  formData.append('status', 'draft');
  formData.append('isFree', 'free');
  formData.append('language', 'English');
  
  // Add the problematic fields that were causing validation errors
  formData.append('learningObjectives', JSON.stringify(['Learn React basics', 'Build components', 'Handle state management']));
  formData.append('hasSubtitles', JSON.stringify(false));
  formData.append('hasCertificate', JSON.stringify(true));
  formData.append('isPublished', JSON.stringify(false));
  
  // Add other optional fields
  formData.append('prerequisites', 'Basic JavaScript knowledge');
  formData.append('targetAudience', 'Beginner developers');
  formData.append('estimatedDuration', '10 hours');

  try {
    console.log('Testing course creation with FormData...');
    console.log('Fields being sent:');
    console.log('- learningObjectives:', JSON.stringify(['Learn React basics', 'Build components', 'Handle state management']));
    console.log('- hasSubtitles:', JSON.stringify(false));
    console.log('- hasCertificate:', JSON.stringify(true));
    
    const response = await fetch('http://localhost:5000/api/v1/courses/create-course/507f1f77bcf86cd799439013', {
      method: 'POST',
      body: formData,
      headers: {
        'Authorization': 'Bearer test-token', // You'll need a valid token
      }
    });

    const result = await response.text();
    console.log('Response status:', response.status);
    console.log('Response body:', result);
    
    if (response.status === 400) {
      console.log('\n❌ Validation error still exists');
      try {
        const errorData = JSON.parse(result);
        console.log('Error details:', JSON.stringify(errorData, null, 2));
      } catch (e) {
        console.log('Raw error response:', result);
      }
    } else if (response.status === 200 || response.status === 201) {
      console.log('\n✅ Course creation successful!');
    } else {
      console.log('\n⚠️  Unexpected response status:', response.status);
    }
    
  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

// Run the test
testCourseCreation();
